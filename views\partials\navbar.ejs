<nav class="bg-white shadow-lg sticky top-0 z-50">
  <div class="max-w-6xl mx-auto px-4">
    <div class="flex justify-between items-center">
      <!-- Logo Section -->
      <div class="flex space-x-7">
        <a href="/" class="flex items-center py-4 group">
          <i class="fas fa-wallet text-blue-600 text-2xl group-hover:scale-110 transition-transform duration-300"></i>
          <span class="font-bold text-gray-800 text-lg ml-2 group-hover:text-blue-600 transition-colors duration-300">Dhanfolio</span>
        </a>
      </div>

      <!-- Mobile menu button -->
      <div class="md:hidden">
        <button class="mobile-menu-button p-2 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500">
          <i class="fas fa-bars text-gray-600 text-xl"></i>
        </button>
      </div>

      <!-- Navigation Links -->
      <div class="hidden md:flex items-center space-x-1">
        <a href="/incomes" class="nav-link group">
          <div class="py-2 px-4 rounded-lg hover:bg-blue-50 transition-all duration-300">
            <i class="fas fa-wallet mr-2 group-hover:text-blue-600"></i>
            <span class="group-hover:text-blue-600">Income</span>
          </div>
        </a>
        
        <a href="/needs" class="nav-link group">
          <div class="py-2 px-4 rounded-lg hover:bg-blue-50 transition-all duration-300">
            <i class="fas fa-house-user mr-2 group-hover:text-blue-600"></i>
            <span class="group-hover:text-blue-600">Needs</span>
          </div>
        </a>
        
        <a href="/wants" class="nav-link group">
          <div class="py-2 px-4 rounded-lg hover:bg-blue-50 transition-all duration-300">
            <i class="fas fa-shopping-cart mr-2 group-hover:text-blue-600"></i>
            <span class="group-hover:text-blue-600">Wants</span>
          </div>
        </a>
        
        <a href="/investments" class="nav-link group">
          <div class="py-2 px-4 rounded-lg hover:bg-blue-50 transition-all duration-300">
            <i class="fas fa-chart-bar mr-2 group-hover:text-blue-600"></i>
            <span class="group-hover:text-blue-600">Investment</span>
          </div>
        </a>
        
        <a href="/donations" class="nav-link group">
          <div class="py-2 px-4 rounded-lg hover:bg-blue-50 transition-all duration-300">
            <i class="fas fa-hand-holding-heart mr-2 group-hover:text-blue-600"></i>
            <span class="group-hover:text-blue-600">Donations</span>
          </div>
        </a>
      </div>
    </div>
  </div>

  <!-- Mobile Menu -->
  <div class="mobile-menu hidden md:hidden">
    <a href="/incomes" class="block py-3 px-4 text-gray-600 hover:bg-blue-50 hover:text-blue-600 transition-all duration-300">
      <i class="fas fa-wallet mr-2"></i>Income
    </a>
    <a href="/needs" class="block py-3 px-4 text-gray-600 hover:bg-blue-50 hover:text-blue-600 transition-all duration-300">
      <i class="fas fa-house-user mr-2"></i>Needs
    </a>
    <a href="/wants" class="block py-3 px-4 text-gray-600 hover:bg-blue-50 hover:text-blue-600 transition-all duration-300">
      <i class="fas fa-shopping-cart mr-2"></i>Wants
    </a>
    <a href="/investments" class="block py-3 px-4 text-gray-600 hover:bg-blue-50 hover:text-blue-600 transition-all duration-300">
      <i class="fas fa-chart-bar mr-2"></i>Investment
    </a>
    <a href="/donations" class="block py-3 px-4 text-gray-600 hover:bg-blue-50 hover:text-blue-600 transition-all duration-300">
      <i class="fas fa-hand-holding-heart mr-2"></i>Donations
    </a>
  </div>
</nav>

<style>
  .nav-link.active div {
    background-color: rgb(239, 246, 255);
    color: rgb(37, 99, 235);
  }
  
  .nav-link.active i {
    color: rgb(37, 99, 235);
  }
</style>

<script>
  // Mobile menu toggle
  document.querySelector('.mobile-menu-button').addEventListener('click', function() {
    document.querySelector('.mobile-menu').classList.toggle('hidden');
  });

  // Active link handling
  document.addEventListener('DOMContentLoaded', function() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
      if (link.getAttribute('href') === currentPath) {
        link.classList.add('active');
      }
    });
  });
</script>